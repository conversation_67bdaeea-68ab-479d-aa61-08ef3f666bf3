// user-center.js
Page({
  data: {
    userInfo: {
      nickname: '旅行者',
      avatar: '/images/icon/user.png'
    },
    userStats: {
      diaryCount: 0,
      favoriteCount: 0,
      routeCount: 0
    },
    quickActions: [
      {
        id: 1,
        icon: '✍️',
        title: '写日记',
        url: '/pages/diary-create/diary-create'
      },
      {
        id: 2,
        icon: '🗺️',
        title: '路线规划',
        url: '/pages/route-plan/route-plan'
      },
      {
        id: 3,
        icon: '🔍',
        title: '找景点',
        url: '/pages/place-search/place-search'
      },
      {
        id: 4,
        icon: '🌟',
        title: '推荐',
        url: '/pages/recommend/recommend'
      }
    ],
    menuGroups: [
      {
        title: '我的内容',
        items: [
          {
            id: 1,
            icon: '📖',
            title: '我的日记',
            desc: '查看我发布的旅行日记',
            url: '/pages/my-diaries/my-diaries'
          },
          {
            id: 2,
            icon: '❤️',
            title: '我的收藏',
            desc: '收藏的景点和日记',
            url: '/pages/favorites/favorites'
          },
          {
            id: 3,
            icon: '🗺️',
            title: '历史路线',
            desc: '查看历史规划路线',
            url: '/pages/route-history/route-history'
          }
        ]
      },
      {
        title: '设置与帮助',
        items: [
          {
            id: 4,
            icon: '⚙️',
            title: '设置',
            desc: '个人设置和偏好',
            url: '/pages/settings/settings'
          },
          {
            id: 5,
            icon: '📞',
            title: '联系我们',
            desc: '获取帮助与客户支持',
            url: '/pages/contact/contact'
          },
          {
            id: 6,
            icon: '📋',
            title: '关于我们',
            desc: '了解鸿雁智游',
            url: '/pages/about/about'
          }
        ]
      }
    ]
  },

  onLoad: function() {
    console.log('个人中心页面加载');
    this.loadUserStats();
  },

  onShow: function() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 4
      });
    }
    this.loadUserStats();
  },
  // 获取用户信息
  getUserProfile: function() {
      const that = this;
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: function(res) {
          that.setData({
            'userInfo.nickname': res.userInfo.nickName,
            'userInfo.avatar': res.userInfo.avatarUrl
          });

          // 保存用户信息到本地存储
          wx.setStorageSync('userInfo', {
            nickname: res.userInfo.nickName,
            avatar: res.userInfo.avatarUrl
          });
        },
        fail: function() {
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
        }
      });
  },

  // 加载用户统计数据
  loadUserStats: function() {
      const that = this;

      // 尝试从本地存储获取用户信息
      const savedUserInfo = wx.getStorageSync('userInfo');
      if (savedUserInfo) {
        this.setData({
          userInfo: savedUserInfo
        });
      }

      // 加载用户日记数量
      wx.request({
        url: 'http://localhost:5000/api/articles/user/1', // 临时用户ID
        method: 'GET',
        success: function(res) {
          console.log('用户日记API响应:', res);
          if (res.data.code === 0 && res.data.data) {
            that.setData({
              'userStats.diaryCount': res.data.data.length || 0
            });
          }
        },
        fail: function(error) {
          console.error('获取用户日记数量失败:', error);
          // 使用模拟数据
          that.setData({
            'userStats.diaryCount': 5
          });
        }
      });

      // 暂时使用模拟数据，后续可以添加实际的API
      this.setData({
        'userStats.favoriteCount': 12,
        'userStats.routeCount': 3
      });
  },

  // 快捷功能点击
  onQuickActionTap: function(e) {
      const url = e.currentTarget.dataset.url;
      if (url) {
        wx.navigateTo({
          url: url
        });
      }
  },

  // 菜单项点击
  onMenuItemTap: function(e) {
      const url = e.currentTarget.dataset.url;
      if (url) {
        if (url.includes('/my-diaries/') || url.includes('/diary-create/') || url.includes('/contact/') || url.includes('/settings/')) {
          // 已存在的页面
          wx.navigateTo({
            url: url
          });
        } else {
          // 未实现的页面
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      }
  },

  // 跳转到日记列表
  goToDiaryList: function() {
      wx.navigateTo({
        url: '/pages/my-diaries/my-diaries'
      });
  },

  // 跳转到收藏页面
  goToFavorites: function() {
      wx.showToast({
        title: '收藏功能开发中',
        icon: 'none'
      });
  },

  // 跳转到路线历史
  goToRouteHistory: function() {
      wx.showToast({
        title: '路线历史功能开发中',
        icon: 'none'
      });
  }
});
