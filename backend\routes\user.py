from flask import Blueprint, request, jsonify, current_app
from models.user import User
from models.article import Article, ArticleFavorite
from models.article_like import ArticleLike
from utils.database import db
import os
from datetime import datetime
from utils.response import success, error, unauthorized
import uuid
from werkzeug.utils import secure_filename
from flask import send_from_directory
from flask import current_app # 确保导入 current_app 用于日志记录
from sqlalchemy import func

user_bp = Blueprint('user', __name__)

# 更新用户头像
@user_bp.route('/avatar', methods=['POST'])
def update_avatar():
    # --- 新增：添加日志记录 ---
    current_app.logger.info("--- Avatar Update Request Received ---")
    current_app.logger.info(f"Request Headers: {request.headers}")
    current_app.logger.info(f"Request Content-Type: {request.content_type}")
    current_app.logger.info(f"Request Form Data: {request.form}")
    current_app.logger.info(f"Request Files Data: {request.files}")
    # --- 新增结束 ---

    user_id = request.form.get('user_id')
    avatar_file = request.files.get('avatar')

    # --- 新增：记录提取到的值 ---
    current_app.logger.info(f"Extracted user_id: {user_id} (Type: {type(user_id)})")
    current_app.logger.info(f"Extracted avatar_file: {avatar_file} (Type: {type(avatar_file)})")
    # --- 新增结束 ---

    if not user_id or not avatar_file:
        # --- 新增：记录验证失败原因 ---
        current_app.logger.error(f"Validation Failed: user_id is '{user_id}', avatar_file is '{avatar_file}'")
        # --- 新增结束 ---
        return error('Missing user_id or avatar file')

    # 检查用户是否存在
    user = User.query.get(user_id)
    if not user:
        current_app.logger.error(f"User not found for user_id: {user_id}") # 添加日志
        return error('User not found')

    # 检查文件类型
    allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    if '.' not in avatar_file.filename or \
       avatar_file.filename.rsplit('.', 1)[1].lower() not in allowed_extensions:
        return error('Invalid file type. Allowed types: ' + ', '.join(allowed_extensions))

    # 生成唯一文件名
    filename = f"avatar_{user_id}_{uuid.uuid4().hex}.{avatar_file.filename.rsplit('.', 1)[1].lower()}"

    # 确保上传目录存在
    upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'avatars')
    try:
        os.makedirs(upload_folder, exist_ok=True)
        current_app.logger.info(f"Upload folder ensured: {upload_folder}") # 添加日志
    except Exception as e:
        current_app.logger.error(f"Error creating upload folder '{upload_folder}': {e}") # 添加日志
        return error('Failed to create upload directory')
    file_path = os.path.join(upload_folder, filename)
    try:
        avatar_file.save(file_path)
        current_app.logger.info(f"Avatar saved to: {file_path}")
        user.avatar = f"/uploads/avatars/{filename}"
        db.session.commit()
        current_app.logger.info(f"User {user_id} avatar updated in DB to: {user.avatar}")
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error saving avatar or updating DB for user_id {user_id}: {e}")
        return error('Failed to save avatar or update database')

    return success({
        'avatar_url': user.avatar
    }, 'Avatar updated successfully')

# 更新用户名
@user_bp.route('/username', methods=['PUT'])
def update_username():
    data = request.get_json()
    user_id = data.get('user_id')
    new_username = data.get('new_username')
    password = data.get('password')

    if not all([user_id, new_username, password]):
        return jsonify({'error': 'Missing required fields'}), 400

    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404

    if not user.check_password(password):
        return jsonify({'error': 'Invalid password'}), 401

    # 检查用户名是否已存在
    if User.query.filter(User.username == new_username, User.user_id != user_id).first():
        return jsonify({'error': 'Username already exists'}), 400

    user.username = new_username
    db.session.commit()

    return jsonify({'message': 'Username updated successfully'}), 200

# 获取用户信息
@user_bp.route('/<int:user_id>', methods=['GET'])
def get_user_info(user_id):
    user = User.query.get(user_id)
    if not user:
        return error('User not found')

    return success({
        'user': user.to_dict()
    }, 'User information retrieved successfully')

# 获取用户收藏的文章
@user_bp.route('/<int:user_id>/favorites', methods=['GET'])
def get_user_favorites(user_id):
    # 检查用户是否存在
    user = User.query.get(user_id)
    if not user:
        return error('User not found')

    # 获取用户的收藏
    favorites = ArticleFavorite.query.filter_by(user_id=user_id).all()

    # 获取收藏的文章详情
    articles = []
    for favorite in favorites:
        article = Article.query.get(favorite.article_id)
        if article:
            # 获取文章作者信息
            author = User.query.get(article.user_id)
            author_name = author.username if author else 'Unknown'

            # 构建文章信息
            article_info = article.to_dict()
            article_info['author'] = author_name
            article_info['favorited_at'] = favorite.created_at.isoformat() if favorite.created_at else None

            articles.append(article_info)

    return success({
        'favorites': articles
    }, 'Favorites retrieved successfully')

# 更新用户邮箱
@user_bp.route('/email', methods=['PUT'])
def update_email():
    data = request.get_json()
    user_id = data.get('user_id')
    new_email = data.get('new_email')
    password = data.get('password')

    if not all([user_id, new_email, password]):
        return error('Missing required fields')

    user = User.query.get(user_id)
    if not user:
        return error('User not found')

    if not user.check_password(password):
        return unauthorized('Invalid password')

    # 检查邮箱是否已存在
    if User.query.filter(User.email == new_email, User.user_id != user_id).first():
        return error('Email already exists')

    user.email = new_email
    db.session.commit()

    return success({}, 'Email updated successfully')

# 更新用户密码
@user_bp.route('/password', methods=['PUT'])
def update_password():
    data = request.get_json()
    user_id = data.get('user_id')
    current_password = data.get('current_password')
    new_password = data.get('new_password')
    confirm_password = data.get('confirm_password')

    if not all([user_id, current_password, new_password, confirm_password]):
        return error('Missing required fields')

    # 检查新密码和确认密码是否一致
    if new_password != confirm_password:
        return error('New password and confirm password do not match')

    # 检查新密码长度
    if len(new_password) < 6:
        return error('Password must be at least 6 characters long')

    user = User.query.get(user_id)
    if not user:
        return error('User not found')

    # 验证当前密码
    if not user.check_password(current_password):
        return unauthorized('Current password is incorrect')

    # 更新密码
    user.set_password(new_password)
    db.session.commit()

    return success({}, 'Password updated successfully')

# 获取用户统计数据
@user_bp.route('/stats', methods=['GET'])
def get_user_stats():
    user_id = request.args.get('user_id')

    if not user_id:
        return error('Missing user_id parameter')

    try:
        user_id = int(user_id)
    except ValueError:
        return error('Invalid user_id format')

    # 检查用户是否存在
    user = User.query.get(user_id)
    if not user:
        return error('User not found')

    try:
        # 获取用户发布的日记数量
        diary_count = Article.query.filter_by(user_id=user_id).count()

        # 获取用户收藏的文章数量
        favorite_count = ArticleFavorite.query.filter_by(user_id=user_id).count()

        # 获取用户文章的总点赞数
        total_likes = db.session.query(func.count(ArticleLike.id)).join(
            Article, ArticleLike.article_id == Article.article_id
        ).filter(Article.user_id == user_id).scalar() or 0

        # 获取用户文章的总浏览数（如果有浏览表的话，这里先用0）
        total_views = 0

        stats = {
            'diaryCount': diary_count,
            'favoriteCount': favorite_count,
            'totalLikes': total_likes,
            'totalViews': total_views,
            'routeCount': 0  # 暂时设为0，后续可以添加路线相关统计
        }

        return success(stats, 'User statistics retrieved successfully')

    except Exception as e:
        current_app.logger.error(f"Error getting user stats for user_id {user_id}: {e}")
        return error('Failed to retrieve user statistics')