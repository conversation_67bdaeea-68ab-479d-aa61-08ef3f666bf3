// diary-detail.js
Page({
  data: {
    diaryId: 0,
    diaryData: null,
    loading: true,
    imageList: [],
    tagList: [],
    isLiked: false,
    isFavorited: false,
    userId: 1 // 临时用户ID
  },

  onLoad: function(options) {
    console.log('日记详情页面参数:', options);

    if (options.id) {
      this.setData({
        diaryId: parseInt(options.id)
      });
      this.loadDiaryDetail();
    } else {
      console.error('未获取到日记ID');
      this.setData({ loading: false });
      wx.showToast({
        title: '日记ID无效',
        icon: 'none'
      });
    }
  },

  // 加载日记详情
  loadDiaryDetail: function() {
    const that = this;
    this.setData({ loading: true });

    console.log('开始加载日记详情，ID:', this.data.diaryId);

    wx.request({
      url: 'http://localhost:5000/api/articles/' + this.data.diaryId,
      method: 'GET',
      success: function(res) {
        console.log('日记详情API完整响应:', res);

        // 检查HTTP状态码
        if (res.statusCode !== 200) {
          console.error('HTTP状态码错误:', res.statusCode);
          that.setData({ loading: false });
          wx.showToast({
            title: `服务器错误 (${res.statusCode})`,
            icon: 'none'
          });
          return;
        }

        // 修复响应格式判断：后端返回 code: 0 表示成功
        if (res.data.code === 0 && res.data.data) {
          const diaryData = res.data.data;
          console.log('日记数据:', diaryData);

          // 处理图片列表
          const imageList = [
            diaryData.image_url,
            diaryData.image_url_2,
            diaryData.image_url_3,
            diaryData.image_url_4,
            diaryData.image_url_5,
            diaryData.image_url_6
          ].filter(function(url) {
            return url && url.trim();
          });

          // 处理标签列表
          let tagList = [];
          if (diaryData.tags) {
            try {
              // 如果tags是数组，直接使用；如果是字符串，尝试解析
              if (Array.isArray(diaryData.tags)) {
                tagList = diaryData.tags;
              } else {
                tagList = JSON.parse(diaryData.tags);
              }
            } catch (e) {
              console.error('解析标签失败:', e);
              tagList = [];
            }
          }

          that.setData({
            diaryData: Object.assign({}, diaryData, {
              created_at: that.formatDate(diaryData.created_at)
            }),
            imageList: imageList,
            tagList: tagList,
            loading: false
          });

          console.log('日记详情加载成功');

          // 检查点赞和收藏状态
          that.checkLikeStatus();
          that.checkFavoriteStatus();
        } else {
          console.error('API返回错误:', res.data);
          that.setData({ loading: false });

          // 根据错误码显示不同的错误信息
          let errorMessage = '加载失败';
          if (res.data.code === 404) {
            errorMessage = '日记不存在';
          } else if (res.data.message) {
            errorMessage = res.data.message;
          }

          wx.showToast({
            title: errorMessage,
            icon: 'none'
          });
        }
      },
      fail: function(error) {
        console.error('网络请求失败:', error);
        that.setData({ loading: false });
        wx.showToast({
          title: '网络连接失败，请检查网络设置',
          icon: 'none'
        });
      }
    });
  },

  // 格式化日期
  formatDate: function(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.getFullYear() + '年' + (date.getMonth() + 1) + '月' + date.getDate() + '日';
  },

  // 预览图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.imageList[index],
      urls: this.data.imageList
    });
  },

  // 检查点赞状态
  checkLikeStatus: function() {
    const that = this;
    wx.request({
      url: 'http://localhost:5000/api/article_like/check',
      method: 'POST',
      data: {
        user_id: this.data.userId,
        article_id: this.data.diaryId
      },
      success: function(res) {
        if (res.data.code === 0) {
          that.setData({
            isLiked: res.data.data.is_liked || false
          });
        }
      },
      fail: function(error) {
        console.error('检查点赞状态失败:', error);
      }
    });
  },

  // 检查收藏状态
  checkFavoriteStatus: function() {
    this.setData({ isFavorited: false });
  },

  // 切换点赞
  toggleLike: function() {
    const that = this;
    const url = this.data.isLiked 
      ? 'http://localhost:5000/api/article_like/unlike'
      : 'http://localhost:5000/api/article_like';

    wx.request({
      url: url,
      method: 'POST',
      data: {
        user_id: this.data.userId,
        article_id: this.data.diaryId
      },
      success: function(res) {
        if (res.data.code === 0) {
          that.setData({
            isLiked: !that.data.isLiked
          });
        }
      },
      fail: function(error) {
        console.error('点赞操作失败:', error);
        wx.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    });
  },

  // 切换收藏
  toggleFavorite: function() {
    this.setData({
      isFavorited: !this.data.isFavorited
    });

    wx.showToast({
      title: this.data.isFavorited ? '已收藏' : '已取消收藏',
      icon: 'success'
    });
  },

  // 分享文章
  shareArticle: function() {
    wx.showShareMenu({
      withShareTicket: true
    });
  },

  // 返回
  goBack: function() {
    wx.navigateBack();
  }
});
